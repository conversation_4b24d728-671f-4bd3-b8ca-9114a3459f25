import { WrapI<PERSON>, VStack, Flex, Text, useColorMode } from '@chakra-ui/react';
import { ChartProp } from '../utils/interface';
import DualLineChart from './DualLineChart';
import { useAppSelector } from '@/store/store';
import { KpiDataWithMeta } from '../utils/interface';

const OverallPerformanceCard = () => {
   const { colorMode } = useColorMode();
   const overallData = useAppSelector(
      (state) => state.kpi.connectorData['overall_metrics'],
   );

   const kpi1 = overallData?.blended_total_revenue;
   const kpi2 = overallData?.blended_ad_spend;

   if (!kpi1 || !kpi2) return null;
   const dualKpiPayload: ChartProp[] = [
      { kpiDetails: kpi1 as KpiDataWithMeta, value: kpi1.current_allData },
      { kpiDetails: kpi2 as KpiDataWithMeta, value: kpi2.current_allData },
   ];
   return (
      <WrapItem
         minWidth={100}
         flex='1.8'
         background={colorMode === 'dark' ? 'gray.800' : 'white'}
         boxShadow={
            colorMode === 'dark'
               ? '1px 1px 10px 1px #00000033'
               : '1px 1px 10px 1px #cccccc33'
         }
         padding={5}
         borderRadius={5}
         position={'relative'}
         /*onMouseOver={() => setShowRightIcons(true)}
      onMouseLeave={() => setShowRightIcons(false)}*/
         className='kpi-item'
      >
         <VStack width='100%' alignItems={'flex-start'}>
            <Flex width='100%' direction='column'>
               <Text fontSize={14} fontWeight={600}>
                  Overall Performance
               </Text>

               <Flex width='100%' alignItems='center'>
                  <DualLineChart data={dualKpiPayload} />
               </Flex>
            </Flex>
         </VStack>
      </WrapItem>
   );
};

export default OverallPerformanceCard;
