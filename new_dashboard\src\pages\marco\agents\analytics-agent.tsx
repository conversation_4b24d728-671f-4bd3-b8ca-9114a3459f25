import React from 'react';
import { cn } from '@/utils';
import { toast } from 'sonner';
import { Spinner } from '@chakra-ui/react';
import { PiChartBar } from 'react-icons/pi';
import Tooltips from '@/components/tooltip';
import { Button } from '@/components/ui/button';
import { useSearchParams } from 'react-router-dom';
import { useEffect, useRef, useState } from 'react';
import { Textarea } from '@/components/ui/textarea';
import { useSidebar } from '@/components/ui/sidebar';
import { Separator } from '@/components/ui/separator';
import { useQueryClient } from '@tanstack/react-query';
import { IoAnalyticsSharp, IoStop } from 'react-icons/io5';
//import CMOModeIcon from '@/assets/icons/cmo-mode-icon.svg';
import { HoverCardArrow } from '@radix-ui/react-hover-card';
import { setCurrentHistory } from '@/store/reducer/marco-reducer';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
   defaultTabListClass,
   defaultTabTriggerClass,
} from '@/constants/defaultClasses';
import {
   HoverCard,
   HoverCardContent,
   HoverCardTrigger,
} from '@/components/ui/hover-card';
import { useCreateNotificationMutation } from '@/components/notifications-popover/notifications-apis';
import { AuthUser } from '../../../types/auth';
import { useAppDispatch, useAppSelector } from '../../../store/store';
import { getSmartSuggestions } from '../utils/analytics-agent/helpers';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import AnalyticAgentChat from '../components/chat-bubble/analytic-agent-chat';
import aiCMOAgentIcon from '../../../assets/image/agents/Motoko - AI CMO.png';
import {
   CMO_QUESTION_SUGGESTIONS,
   DEFAULT_QUESTION_SUGGESTIONS,
} from '../utils/analytics-agent/constants';
import {
   abandonJob,
   AnalyticsAgentChat,
   checkChatProgress,
   startDirectResponse,
} from '../../../api/service/agentic-workflow/analytics-agent';
import {
   useFetchChatHistoryQuery,
   useTrackFeatureUsageMutation,
   useFetchFeatureUsageQuery,
   useFetchHistoryQuery,
} from '../apis/analytics-agent-apis';
import {
   setCurrentSessionID,
   clearKpiPrompts,
   setCurrentMode,
   setRunningChats,
   clearKpiMetadata,
   ChatProgress,
   setChatProgress,
} from '../../../store/reducer/analytics-agent-reducer';
import { useTypewriterPlaceholder } from '../utils/analytics-agent/typewrite-placeholder';
import { FiRadio } from 'react-icons/fi';

const USAGE_LIMITS: Record<string, number> = {
   data_analyst: 100,
   cmo: 20,
};

const POLL_INTERVAL = 5000;

const AnalyticsAgent = () => {
   const dispatch = useAppDispatch();
   const queryClient = useQueryClient();

   const { setOpen } = useSidebar();

   const hasRun = useRef<boolean>(false);
   const jobIntervals = useRef(new Map<string, NodeJS.Timeout>());

   const [searchParams] = useSearchParams();
   const paramsQuery = searchParams.get('query') || '';
   const paramsAiPrompt = searchParams.get('aiPrompt') || '';

   const messagesEndRef = useRef<HTMLDivElement>(null);
   const { client_id, user_id } =
      LocalStorageService.getItem<AuthUser>(Keys.FlableUserDetails) ?? {};

   const { currentHistory } = useAppSelector((state) => state.marco);
   const { connectionDetails } = useAppSelector((state) => state.media);
   const {
      currentSessionID,
      currentMode,
      chatProgress,
      notificationPermission,
      kpiDisplayPrompt,
      kpiAiPrompt,
      runningChats,
      kpiMetadata,
   } = useAppSelector((state) => state.analyticsAgent);

   const [prompt, setPrompt] = useState<string>('');
   const [suggestions, setSuggestions] = useState<string[]>([]);
   const [queryFetching, setQueryFetching] = useState<boolean>(false);

   const { refetch: refetchHistory } = useFetchHistoryQuery();
   const { data: featureUsage, refetch: refetchFeatureUsage } =
      useFetchFeatureUsageQuery();
   const { data: currentChatHistory, refetch: refetchChatHistory } =
      useFetchChatHistoryQuery();

   const { mutateAsync: sendNotification } = useCreateNotificationMutation();
   const { mutateAsync: updateFeatureUsage } = useTrackFeatureUsageMutation();

   const showTypewriter =
      (!currentChatHistory || currentChatHistory.length === 0) &&
      !queryFetching &&
      runningChats.length === 0;

   const placeholderText = useTypewriterPlaceholder({
      prompts: suggestions,
      prefix: 'Ask AI CMO ',
      typingSpeed: 60,
      deletingSpeed: 30,
      pauseDuration: 1800,
      maxWords: 6,
      enabled: showTypewriter,
   });
   const pollProgressStatus = async (
      chat_id: string,
      session_id: string,
   ): Promise<ChatProgress> => {
      return new Promise((resolve, reject) => {
         const interval = setInterval(() => {
            void (async () => {
               try {
                  const res = await checkChatProgress(
                     String(chat_id),
                     String(session_id),
                     String(client_id),
                  );

                  if (res?.current_status === 'running') {
                     dispatch(setChatProgress({ [chat_id]: res }));
                  } else if (res?.current_status === 'completed') {
                     dispatch(setChatProgress({ [chat_id]: res }));
                     clearInterval(interval);
                     jobIntervals.current.delete(`${chat_id}${session_id}`);
                     const newRunningChatIds = runningChats.filter(
                        (id) => id !== String(chat_id),
                     );
                     dispatch(setRunningChats(newRunningChatIds));
                     await refetchChatHistory();
                     resolve(res);
                  } else if (res?.current_status === 'failed') {
                     clearInterval(interval);
                     jobIntervals.current.delete(`${chat_id}${session_id}`);
                     const newRunningChatIds = runningChats.filter(
                        (id) => id !== String(chat_id),
                     );
                     dispatch(setRunningChats(newRunningChatIds));
                     await refetchChatHistory();
                     reject(new Error('Job failed'));
                  }
               } catch (err) {
                  clearInterval(interval);
                  jobIntervals.current.delete(`${chat_id}${session_id}`);
                  reject(err);
               }
            })();
         }, POLL_INTERVAL);

         jobIntervals.current.set(`${chat_id}${session_id}`, interval);
      });
   };

   const handleStopChat = async (currentChatHistory: AnalyticsAgentChat[]) => {
      const chat_id = currentChatHistory?.find((chat) =>
         runningChats.includes(chat.chat_id),
      )?.chat_id;

      if (!chat_id) {
         return;
      }

      const result = await abandonJob(
         String(chat_id),
         String(currentSessionID),
      );

      if (result?.status === 'success') {
         const interval = jobIntervals.current.get(chat_id);

         if (interval) {
            clearInterval(interval);
            jobIntervals.current.delete(chat_id);
         }

         dispatch(
            setRunningChats([...runningChats.filter((id) => id !== chat_id)]),
         );
         await refetchChatHistory();
      }
   };

   const handlePromptChange = (
      event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
   ) => {
      setPrompt(event.target.value);
   };

   const handleKeyDown = async (
      event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>,
   ) => {
      if (event.key === 'Enter') {
         event.preventDefault();
         await handleSendPrompt();
      }
   };

   const handleSampleQuestionClick = async (question: string) => {
      await handleSendPrompt(question).catch(console.log);
   };

   const handleSendPrompt = async (
      userVisiblePrompt?: string,
      actualAiPrompt?: string,
   ) => {
      const featureEnabled =
         featureUsage &&
         featureUsage.length > 0 &&
         featureUsage.find((obj) => obj.mode === currentMode) &&
         !featureUsage.find((obj) => obj.mode === currentMode)?.is_enabled;

      if (featureEnabled) {
         toast.error(
            `Your free limit has expired for ${currentMode
               .split('-')
               .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
               .join(
                  ' ',
               )} mode. Please contact Flable support and upgrade to continue using the Analytics Agent.`,
         );
         return;
      }

      const currentSessionChats = currentHistory['analytics-agent'].find(
         (item) => item.session_id === currentSessionID,
      )?.chat_ids;

      if (
         currentSessionChats &&
         currentSessionChats.some((item) => runningChats.includes(item))
      ) {
         toast.error('Please wait for the current chat to complete.');
         return;
      }

      setOpen(false); // Closes the sidebar

      const displayText = kpiDisplayPrompt || userVisiblePrompt || prompt;
      const aiText = kpiAiPrompt || actualAiPrompt || displayText;

      if (!displayText || !aiText) {
         toast.error('Please enter your query.');
         setPrompt('');
         return;
      }

      const newChatId = `${new Date().getTime()}`;
      const sessionId = !currentSessionID ? `Z${Date.now()}` : currentSessionID;

      setPrompt('');
      dispatch(setCurrentSessionID(sessionId));
      dispatch(clearKpiPrompts());
      dispatch(setRunningChats([...runningChats, String(newChatId)]));

      const lastFiveChats = currentChatHistory
         ?.sort(
            (a: AnalyticsAgentChat, b: AnalyticsAgentChat) =>
               new Date(a.created_at).getTime() -
               new Date(b.created_at).getTime(),
         )
         .slice(0, 5);

      const messageHistory =
         lastFiveChats
            ?.map((item: AnalyticsAgentChat) => [
               { role: 'user', content: item.user_query },
               { role: 'assistant', content: item.final_response },
            ])
            .flat() || [];

      const context_variables = {
         ...lastFiveChats?.[lastFiveChats.length - 1]?.context_snapshot,
      };

      const queryJobPayload = {
         text: aiText,
         mode: currentMode === 'data-analyst' ? 'data_analyst' : 'cmo',
         prompt: displayText,
         session_id: sessionId,
         user_id: `${user_id}`,
         chat_id: String(newChatId),
         client_id: client_id as string,
         kpi_metadata: kpiMetadata || {},
         message_history: messageHistory,
         context_variables: context_variables,
      };

      setQueryFetching(true);
      const finalResponse = await startDirectResponse(queryJobPayload);
      await refetchChatHistory();
      setQueryFetching(false);
      const updatedHistory = await refetchHistory();

      dispatch(
         setCurrentHistory({
            agent: 'analytics-agent',
            history: updatedHistory.data || [],
         }),
      );

      if (finalResponse?.status === 'pending') {
         const result = pollProgressStatus(newChatId, sessionId);

         if ((await result).current_status === 'success') {
            dispatch(clearKpiMetadata());

            const updateFeatureUsagePayload = {
               client_id: client_id || '',
               user_id: user_id || '',
               feature_name: 'analytics_agent',
               feature_type: 'agent',
               mode: currentMode,
            };

            const sendNotificationPayload = {
               client_id: client_id || '',
               user_id: user_id || '',
               notification_title: 'Your analysis is ready.',
               notification_message: `Regarding: "${displayText}"`,
               notification_type: 'analytics_agent',
               notification_data: {
                  session_id: sessionId,
                  chat_id: newChatId,
                  mode: currentMode,
               },
            };

            const newRunningChatIds = runningChats.filter(
               (id) => id !== String(newChatId),
            );

            dispatch(setRunningChats(newRunningChatIds));
            await refetchChatHistory();
            await updateFeatureUsage(updateFeatureUsagePayload);
            await new Promise((resolve) => setTimeout(resolve, 300));
            await refetchFeatureUsage();
            const newHistoryData = await refetchHistory();

            dispatch(
               setCurrentHistory({
                  agent: 'analytics-agent',
                  history: newHistoryData.data || [],
               }),
            );

            if (currentMode === 'cmo') {
               await sendNotification(sendNotificationPayload);
               await queryClient.invalidateQueries({
                  queryKey: ['notifications'],
               });

               if (notificationPermission === 'granted') {
                  new Notification('Analysis Complete!', {
                     body: 'Your deep analysis is complete. Please check the results.',
                  });
               }

               toast.success(
                  'Deep analysis is complete. Please check the results.',
               );
            }
         }
      }
   };

   useEffect(() => {
      if (
         !hasRun.current &&
         currentHistory &&
         currentHistory?.['analytics-agent'].length > 0
      ) {
         hasRun.current = true;

         currentHistory?.['analytics-agent'].forEach((session) => {
            void (async () => {
               if (
                  session &&
                  session.pending_chats &&
                  session.pending_chats.length > 0 &&
                  !runningChats.some((id) =>
                     session.pending_chats?.some((chat) => chat.chat_id === id),
                  )
               ) {
                  for (const chat of session.pending_chats) {
                     dispatch(setRunningChats([...runningChats, chat.chat_id]));
                     dispatch(
                        setCurrentMode(
                           chat.question_mode.trim().toLowerCase() as
                              | 'cmo'
                              | 'data-analyst',
                        ),
                     );

                     try {
                        const result = await pollProgressStatus(
                           chat.chat_id,
                           session.session_id,
                        );

                        if (result.current_status === 'completed') {
                           const updateFeatureUsagePayload = {
                              client_id: client_id || '',
                              user_id: user_id || '',
                              feature_name: 'analytics_agent',
                              feature_type: 'agent',
                              mode: chat.question_mode,
                           };

                           const sendNotificationPayload = {
                              client_id: client_id || '',
                              user_id: user_id || '',
                              notification_title: 'Your analysis is ready.',
                              notification_message: `Regarding: "${chat.user_query}"`,
                              notification_type: 'analytics_agent',
                              notification_data: {
                                 session_id: session.session_id,
                                 chat_id: chat.chat_id,
                                 mode: currentMode,
                              },
                           };

                           const newRunningChatIds = runningChats.filter(
                              (id) => id !== String(chat.chat_id),
                           );

                           dispatch(setRunningChats(newRunningChatIds));
                           await refetchChatHistory();
                           await updateFeatureUsage(updateFeatureUsagePayload);
                           await new Promise((resolve) =>
                              setTimeout(resolve, 300),
                           );
                           await refetchFeatureUsage();
                           const newHistoryData = await refetchHistory();

                           dispatch(
                              setCurrentHistory({
                                 agent: 'analytics-agent',
                                 history: newHistoryData.data || [],
                              }),
                           );

                           if (chat.question_mode.includes('cmo')) {
                              await sendNotification(sendNotificationPayload);
                              await queryClient.invalidateQueries({
                                 queryKey: ['notifications'],
                              });

                              if (notificationPermission === 'granted') {
                                 new Notification('Analysis Complete!', {
                                    body: 'Your deep analysis is complete. Please check the results.',
                                 });
                              }

                              toast.success(
                                 'Deep analysis is complete. Please check the results.',
                              );
                           }
                        }
                     } catch (err) {
                        console.error('Job polling failed:', err);
                     }
                  }
               }
            })();
         });
      }
   }, [currentHistory]);

   useEffect(() => {
      if (kpiDisplayPrompt && kpiAiPrompt) {
         handleSendPrompt(kpiDisplayPrompt, kpiAiPrompt)
            .catch(console.log)
            .finally(() => {
               dispatch(clearKpiPrompts());
            });
      } else if (paramsQuery || paramsAiPrompt) {
         handleSendPrompt(paramsQuery, paramsAiPrompt)
            .catch(console.log)
            .finally(() => {
               const url = new URL(window.location.href);
               url.search = '';
               window.history.replaceState({}, document.title, url.toString());
            });
      }
   }, [kpiAiPrompt, paramsQuery, paramsAiPrompt, kpiDisplayPrompt]);

   useEffect(() => {
      const smartSuggestions = getSmartSuggestions(
         connectionDetails,
         DEFAULT_QUESTION_SUGGESTIONS,
      );

      currentMode === 'cmo'
         ? setSuggestions(CMO_QUESTION_SUGGESTIONS)
         : setSuggestions(smartSuggestions);
   }, [currentMode, connectionDetails]);

   useEffect(() => {
      const el = messagesEndRef.current;
      if (!el) return;

      const rect = el.getBoundingClientRect();
      const inView =
         rect.top >= 0 &&
         rect.bottom <=
            (window.innerHeight || document.documentElement.clientHeight);

      if (inView) {
         // Only scroll if the element is visible
         setTimeout(() => {
            el.scrollIntoView({ behavior: 'smooth' });
         }, 0);
      }
   }, [currentChatHistory, queryFetching, chatProgress]);

   return (
      <div
         className={`w-full h-full flex flex-col items-center ${!currentChatHistory || currentChatHistory.length < 1 ? 'justify-center' : ''}`}
      >
         <>
            {queryFetching &&
            (!currentChatHistory || currentChatHistory.length < 1) ? (
               <div className='flex flex-col items-center justify-center py-12 space-y-4 text-center'>
                  <p className='text-lg font-semibold text-gray-800'>
                     Connecting to{' '}
                     <span className='text-blue-600 drop-shadow-[0_0_6px_rgba(59,130,246,0.6)]'>
                        AI&nbsp;CMO
                     </span>
                  </p>
                  <Spinner className='w-7 h-7 text-blue-600 animate-spin' />
               </div>
            ) : (
               <>
                  <div
                     className={`scrollbaar-hidden w-[95%] max-w-[900px] flex flex-col items-center gap-[5px] overflow-auto pt-[0px] md:pt-[20px] ${currentChatHistory && currentChatHistory.length > 0 ? 'h-full' : ''}`}
                  >
                     {(!currentChatHistory ||
                        currentChatHistory?.length === 0) && (
                        <>
                           <div className='w-40 h-40 overflow-hidden'>
                              <img
                                 src={aiCMOAgentIcon}
                                 alt='AI-CMO'
                                 className='w-36'
                              />
                           </div>
                           <p className='w-full text-center font-bold text-[16px] md:text-[20px] text-black p-[10px] max-w-[750px]'>
                              AI CMO
                           </p>
                           <div className='w-full text-center text-[12px] md:text-[16px] text-black p-[10px] max-w-[500px]'>
                              <p>
                                 I'm trained to dive deep into your data, cut
                                 through the clutter, spot what's driving
                                 results, and make smarter, faster decisions
                                 with confidence.
                              </p>
                              <div className='text-center text-[12px] md:text-[14px] text-black mt-3 md:mt-5 mb-2'>
                                 👉 Try a sample question to get started.
                              </div>
                           </div>
                           <div className='flex flex-wrap gap-2 items-center justify-center max-w-[900px] text-[12px] md:text-[14px] mx-4 mb-10'>
                              {suggestions.map((question, idx) => (
                                 <React.Fragment key={idx}>
                                    <Tooltips
                                       content={question}
                                       className='bg-gray-500 text-white w-max max-w-[400px] fit-c para5'
                                    >
                                       <div
                                          className='flex-1 w-full md:w-[180px] md:min-h-[95px] max-h-[95px] border-[1px] border-gray-300 text-left rounded-[15px] px-[15px] py-[5px] cursor-pointer shadow-md font-semibold bg-white hover:bg-gray-100 hover:text-black line-clamp-4 overflow-hidden text-ellipsis'
                                          onClick={() =>
                                             void handleSampleQuestionClick(
                                                question,
                                             )
                                          }
                                       >
                                          {question}
                                       </div>
                                    </Tooltips>
                                 </React.Fragment>
                              ))}
                           </div>
                        </>
                     )}
                     {currentChatHistory && currentChatHistory.length > 0 && (
                        <div className='w-full flex-1 justify-center overflow-y-auto'>
                           <div className='w-full flex flex-col justify-start max-w-[950px] overflow-auto'>
                              <div className='message'>
                                 <AnalyticAgentChat
                                    currentSessionChats={currentChatHistory}
                                    queryFetching={queryFetching}
                                    handleSendPrompt={handleSendPrompt}
                                 />
                                 <div ref={messagesEndRef}></div>
                              </div>
                           </div>
                        </div>
                     )}
                  </div>
                  <div className='flex flex-col items-center w-[95%] max-w-[900px] my-[10px] md:my-[20px] flex-nowrap justify-between gap-2 bg-white'>
                     <div className='w-full shadow-md rounded-xl border-2'>
                        <Textarea
                           rows={1}
                           placeholder={
                              placeholderText ||
                              'Ask about trends, metrics, or performance...'
                           }
                           className='resize-none max-h-[150px] overflow-auto !text-[12px] md:!text-[16px] shadow-none border-none focus:ring-0 focus:border-none :hover:border-none focus-visible:border-0 focus-visible:ring-0 focus-visible:outline-none'
                           value={prompt}
                           onKeyDown={(e) => {
                              if (e.key === 'Enter' && e.shiftKey) {
                                 return;
                              }
                              void handleKeyDown(e);
                           }}
                           onChange={handlePromptChange}
                        />
                        <div className='flex items-center justify-between rounded-xl p-2 bg-white'>
                           <Tabs
                              defaultValue={currentMode}
                              value={currentMode}
                              onValueChange={(value) => {
                                 dispatch(
                                    setCurrentMode(
                                       value as 'data-analyst' | 'cmo',
                                    ),
                                 );
                              }}
                           >
                              <TabsList
                                 className={cn(
                                    defaultTabListClass,
                                    'h-[45px] w-[80px] rounded-md bg-[#E9E1FF] gap-[3px]',
                                 )}
                              >
                                 <HoverCard openDelay={200} closeDelay={200}>
                                    <HoverCardTrigger className='w-[35px] h-[35px] p-0'>
                                       <TabsTrigger
                                          value='data-analyst'
                                          className={cn(
                                             defaultTabTriggerClass,
                                             'rounded-md w-full hover:cursor-pointer data-[state=inactive]:border-none data-[state=active]:!border-[#7F56D9] data-[state=active]:!bg-[#ffffff]',
                                          )}
                                       >
                                          <PiChartBar color='#7F56D9' />
                                       </TabsTrigger>
                                    </HoverCardTrigger>
                                    <HoverCardContent className='bg-black w-[300px] text-white'>
                                       <p className='text-md'>
                                          Fast Analysis ~ 1min
                                       </p>
                                       <Separator className='my-2' />
                                       <p className='text-sm'>
                                          Focuses on analyzing single-domain
                                          data to generate actionable insights
                                          and recommendations.
                                       </p>
                                       <p className='text-xs mt-1 text-gray-400'>
                                          {USAGE_LIMITS.data_analyst -
                                             ((featureUsage &&
                                                featureUsage?.find(
                                                   (obj) =>
                                                      obj.mode ===
                                                      'data-analyst',
                                                )?.no_of_calls) ||
                                                0)}{' '}
                                          remaining in the free trial.
                                       </p>
                                       <HoverCardArrow className='fill-black' />
                                    </HoverCardContent>
                                 </HoverCard>
                                 <HoverCard openDelay={200} closeDelay={200}>
                                    <HoverCardTrigger className='w-[35px] h-[35px] p-0'>
                                       <TabsTrigger
                                          value='cmo'
                                          className={cn(
                                             defaultTabTriggerClass,
                                             'rounded-md w-full p-2',
                                             'hover:cursor-pointer data-[state=inactive]:border-none data-[state=active]:!border-[#7F56D9] data-[state=active]:!bg-[#ffffff]',
                                          )}
                                       >
                                          <div className='w-[20px] h-[20px] block'>
                                             <FiRadio color='#7F56D9' />
                                          </div>
                                       </TabsTrigger>
                                    </HoverCardTrigger>
                                    <HoverCardContent className='bg-black w-[300px] text-white'>
                                       <p className='text-md'>
                                          Deep Analysis ~ 8mins
                                       </p>
                                       <Separator className='my-2' />
                                       <p className='text-sm'>
                                          Plans, analyzes, and diagnoses
                                          cross-domain performance to uncover
                                          what's working—and what's not.
                                          Delivers strategic{' '}
                                          <strong>prescriptions</strong> to help
                                          you act faster and grow smarter.
                                       </p>
                                       <p className='text-xs mt-1 text-gray-400'>
                                          {USAGE_LIMITS.cmo -
                                             ((featureUsage &&
                                                featureUsage?.find(
                                                   (obj) => obj.mode === 'cmo',
                                                )?.no_of_calls) ||
                                                0)}{' '}
                                          remaining in the free trial.
                                       </p>
                                       <HoverCardArrow className='fill-black' />
                                    </HoverCardContent>
                                 </HoverCard>
                              </TabsList>
                           </Tabs>
                           {runningChats &&
                           runningChats.length > 0 &&
                           currentChatHistory?.some((chat) =>
                              runningChats.includes(chat.chat_id),
                           ) ? (
                              <Button
                                 onClick={() =>
                                    void handleStopChat(currentChatHistory)
                                 }
                                 className='w-[40px] h-[40px] bg-[#7F56D9] text-white rounded-md shadow-md hover:bg-[#6C47C7] disabled:opacity-50 hover:cursor-pointer'
                              >
                                 <IoStop style={{ height: 24, width: 24 }} />
                              </Button>
                           ) : (
                              <Button
                                 onClick={() => void handleSendPrompt()}
                                 className='w-[40px] h-[40px] bg-[#7F56D9] text-white rounded-md shadow-md hover:bg-[#6C47C7] disabled:opacity-50 hover:cursor-pointer'
                              >
                                 <IoAnalyticsSharp
                                    style={{ height: 24, width: 24 }}
                                 />
                              </Button>
                           )}
                        </div>
                     </div>
                     <p className='text-[10px] md:text-[12px] text-gray-500 text-center'>
                        Agent can make mistakes. Please double-check responses.
                     </p>
                  </div>
               </>
            )}
         </>
      </div>
   );
};

export default AnalyticsAgent;
