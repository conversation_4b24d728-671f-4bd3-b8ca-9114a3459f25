import { Button } from '@/components/ui/button';
import {
   <PERSON><PERSON>,
   <PERSON>alogClose,
   <PERSON>alog<PERSON>ontent,
   <PERSON><PERSON><PERSON>ooter,
   <PERSON><PERSON>Header,
   <PERSON>alogTitle,
   DialogTrigger,
} from '@/components/ui/dialog';
import {
   Select,
   SelectContent,
   SelectGroup,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
   useCreateBookmarkMutation,
   useFetchBookmarksQuery,
   useUpdateBookmarkMutation,
} from '../../apis/analytics-agent-apis';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import { AuthUser } from '@/types/auth';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { cn } from '@/utils';
import { Form } from '../cards/analytics-bookmarks';
import { MultiSelect } from '@/components/ui/multi-select';
import { useAppSelector } from '@/store/store';
import { toast } from 'sonner';
import { Badge } from '@/components/ui/badge';
import { PiCheckBold, PiWarningCircleBold, PiX } from 'react-icons/pi';
import { useEffect, useRef, useState } from 'react';
import { VALID_EMAILS_PATTERN } from '@/utils/strings/settings-strings';

interface AddPromptDialogProps {
   children?: React.ReactNode;
   form: Form;
   setForm: React.Dispatch<React.SetStateAction<Form>>;
   open: boolean;
   setOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const DAYS = [
   {
      value: 'monday',
      label: 'Monday',
   },
   {
      value: 'tuesday',
      label: 'Tuesday',
   },
   {
      value: 'wednesday',
      label: 'Wednesday',
   },
   {
      value: 'thursday',
      label: 'Thursday',
   },
   {
      value: 'friday',
      label: 'Friday',
   },
   {
      value: 'saturday',
      label: 'Saturday',
   },
   {
      value: 'sunday',
      label: 'Sunday',
   },
];

const AddPromptDialog = (props: AddPromptDialogProps) => {
   const spanRef = useRef<HTMLSpanElement>(null);

   const { client_id, user_id, email } = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   ) as AuthUser;

   const { generalSettings } = useAppSelector((state) => state.settings);

   const { form, setForm, open, setOpen } = props;

   const [inputWidth, setInputWidth] = useState(20);

   const { refetch: refetchBookmarks } = useFetchBookmarksQuery();

   const { mutateAsync: updatePrompt } = useUpdateBookmarkMutation();
   const { mutateAsync: addPrompt } = useCreateBookmarkMutation();

   const handlePromptChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      setForm((prev: Form) => ({ ...prev, prompt: e.target.value }));
   };

   const handleModeChange = (value: string) => {
      setForm((prev: Form) => ({ ...prev, mode: value }));
   };

   const handleAutoRunChange = (checked: boolean) => {
      setForm((prev: Form) => ({ ...prev, auto_run: checked }));
   };

   const handleTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setForm((prev: Form) => ({ ...prev, time: e.target.value }));
   };

   const handleDaysChange = (days: string[]) => {
      setForm((prev: Form) => ({ ...prev, days }));
   };

   const handleEmailChange = (
      e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
   ) => {
      const { name, value } = e.target;
      setForm((prev) => ({ ...prev, [name]: value }));
   };

   const handleClose = () => {
      setOpen(false);
      setForm({
         id: '',
         prompt: '',
         mode: 'data-analyst',
         auto_run: false,
         time: '09:00',
         days: [],
         new_recipient: '',
         email: [{ email, valid: true }],
      });
   };

   const addUpdatePrompt = async () => {
      if (!form.prompt || (form.auto_run && !form.days.length)) {
         toast.error('Missing required fields');
         return;
      }

      let validRecipient = false;
      if (form.new_recipient) {
         validRecipient = addRecipient();
      } else {
         if (!(form.email.length > 0)) {
            toast.error('Please add at least one email recipient');
         }
         validRecipient = form.email.length > 0;
      }

      if (!validRecipient) {
         return;
      }

      if (form.email.some((recipient) => recipient.valid === false)) {
         toast.error('Please enter valid email addresses for all recipients');
         return;
      }

      const payload = {
         prompt: form.prompt,
         auto_run: form.auto_run,
         time: form.time,
         days: form.days,
         mode: form.mode,
         client_id: String(client_id),
         user_id: String(user_id),
         user_timezone: generalSettings.timezone_name || 'UTC',
         email: form.email.map((x) => x.email).join(',') || '',
      };

      if (form.id) {
         await updatePrompt({ ...payload, id: form.id });
      } else {
         await addPrompt(payload);
      }

      await refetchBookmarks();

      setOpen(false);
      setForm({
         prompt: '',
         mode: 'data-analyst',
         auto_run: false,
         time: '09:00',
         days: [],
         new_recipient: '',
         email: [{ email, valid: true }],
      });
   };

   const focusToInput = () => {
      const inputElement = document.getElementById('new_recipient');
      if (inputElement) {
         inputElement.focus();
      }
   };

   const addRecipient = () => {
      if (!form.new_recipient) {
         toast.error('Please enter a valid email address');
         return false;
      }

      const result = VALID_EMAILS_PATTERN.test(form.new_recipient);

      const emailExists = form.email.some(
         (recipient) => recipient.email === form.new_recipient,
      );

      if (emailExists) {
         toast.info('Email already added');
         setForm((prevForm) => ({
            ...prevForm,
            new_recipient: '',
         }));
         return false;
      }

      setForm((prevForm) => ({
         ...prevForm,
         email: [
            ...prevForm.email,
            { email: form.new_recipient, valid: result },
         ],
         new_recipient: '',
      }));

      if (!result) {
         toast.error('Please enter a valid email address');
         return false;
      }

      return true;
   };

   const deleteRecipient = (idx: number) => {
      const newData = [...form.email];
      newData.splice(idx, 1);

      setForm((prevForm) => {
         return { ...prevForm, email: newData };
      });
   };

   const removeLastRecipient = () => {
      if (form.email.length === 0) {
         return;
      }

      setForm((prevForm) => ({
         ...prevForm,
         email: prevForm.email.slice(0, -1),
      }));
   };

   const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (
         e.key === ',' ||
         e.key === 'Enter' ||
         e.key === 'Tab' ||
         e.key === ' '
      ) {
         e.preventDefault();
         addRecipient();
      }

      if (e.key === 'Backspace' && !form.new_recipient) {
         e.preventDefault();
         removeLastRecipient();
      }
   };

   useEffect(() => {
      if (spanRef.current) {
         const newWidth = spanRef.current.offsetWidth + 10;
         setInputWidth(newWidth < 20 ? 20 : newWidth);
      }
   }, [form.new_recipient]);

   return (
      <Dialog
         open={open}
         onOpenChange={(open: boolean) => {
            setOpen(open);
            if (!open) {
               handleClose();
            }
         }}
      >
         <DialogTrigger>
            {props.children || (
               <Button
                  size='lg'
                  className='bg-[#7F56D9] hover:bg-[#6941C6] text-white font-semibold'
               >
                  <span className='text-xs'>Add Prompt</span>
               </Button>
            )}
         </DialogTrigger>
         <DialogContent className='sm:max-w-[600px] bg-white'>
            <DialogHeader>
               <DialogTitle className='font-bold'>
                  {form?.id ? 'Update Prompt' : 'Add Prompt'}
               </DialogTitle>
            </DialogHeader>
            <div className='grid gap-4'>
               <div className='grid gap-2'>
                  <Label htmlFor='prompt' className='text-sm font-semibold'>
                     Prompt
                  </Label>
                  <Textarea
                     id='prompt'
                     name='prompt'
                     placeholder='Type your prompt here'
                     className='resize-none'
                     value={form.prompt}
                     onChange={handlePromptChange}
                  />
               </div>
               <div className='grid gap-4'>
                  <Label
                     htmlFor='email'
                     className='text-[14px] ml-1 font-semibold gap-1'
                  >
                     Email Recipients<span className='text-red-500'>*</span>
                  </Label>
                  <div
                     onClick={focusToInput}
                     className='w-full h-full overflow-x-scroll hover:cursor-text flex items-start flex-wrap gap-1 rounded-[5px] p-1 border'
                  >
                     {form.email.length > 0 &&
                        form.email.map((recipient, index) => (
                           <Badge
                              key={index}
                              className={`text-[12px] text-white px-2 py-0 mt-1 ${
                                 recipient.valid ? 'bg-green-700' : 'bg-red-600'
                              } font-semibold rounded-full`}
                           >
                              <div className='flex items-center gap-1'>
                                 {recipient.valid ? (
                                    <PiCheckBold />
                                 ) : (
                                    <PiWarningCircleBold size={18} />
                                 )}
                                 {recipient.email}
                              </div>
                              <Button
                                 variant='ghost'
                                 className={`${
                                    recipient.valid
                                       ? 'hover:bg-green-700'
                                       : 'hover:bg-red-600'
                                 } hover:text-gray-300 hover:cursor-pointer w-[22px] text-[12px] p-0 h-[22px]`}
                                 onClick={() => deleteRecipient(index)}
                              >
                                 <PiX />
                              </Button>
                           </Badge>
                        ))}
                     <div className='relative'>
                        <span
                           ref={spanRef}
                           className='invisible whitespace-pre absolute top-0 left-0 z-[-1] text-[14px] px-2'
                        >
                           {form.new_recipient || ' '}
                        </span>
                        <Input
                           className='ml-0 px-2 border-0 shadow-none text-[14px] focus-visible:ring-0 focus-visible:outline-none'
                           autoComplete='off'
                           type='text'
                           id='new_recipient'
                           name='new_recipient'
                           value={form.new_recipient}
                           onKeyDown={handleKeyDown}
                           onChange={handleEmailChange}
                           style={{
                              width: `${inputWidth}px`,
                              minWidth: '30px',
                              maxWidth: '100%',
                           }}
                        />
                     </div>
                  </div>
               </div>
               <div className='grid grid-cols-2 gap-2'>
                  <div className='grid gap-2 col-span-1'>
                     <Label htmlFor='mode' className='text-sm font-semibold'>
                        Mode
                     </Label>
                     <Select
                        name='mode'
                        defaultValue='data-analyst'
                        value={form.mode}
                        onValueChange={handleModeChange}
                     >
                        <SelectTrigger className='w-[180px]'>
                           <SelectValue placeholder='Select mode' />
                        </SelectTrigger>
                        <SelectContent className='bg-white'>
                           <SelectGroup>
                              <SelectItem value='data-analyst'>
                                 Fast Analysis
                              </SelectItem>
                              <SelectItem value='cmo'>Deep Analysis</SelectItem>
                           </SelectGroup>
                        </SelectContent>
                     </Select>
                  </div>
                  <div className='grid gap-2 col-span-1'>
                     <Label
                        htmlFor='auto_run'
                        className='text-sm font-semibold'
                     >
                        Enable AutoRun
                     </Label>
                     <Switch
                        id='auto_run'
                        name='auto_run'
                        checked={form.auto_run}
                        onCheckedChange={handleAutoRunChange}
                     />
                  </div>
               </div>
               {form.auto_run && (
                  <div className='grid grid-cols-2 gap-2'>
                     <div className='grid gap-2 col-span-1'>
                        <Label htmlFor='time' className='text-sm font-semibold'>
                           Time
                        </Label>
                        <Input
                           id='alert_time'
                           type='time'
                           name='alert_time'
                           step='1'
                           defaultValue='09:30'
                           className={cn(
                              'h-[40px] border shadow-none focus-visible:ring-0 focus-visible:outline-none',
                              'bg-background appearance-none [&::-webkit-calendar-picker-indicator]:hidden [&::-webkit-calendar-picker-indicator]:appearance-none',
                           )}
                           value={form.time}
                           onChange={handleTimeChange}
                        />
                     </div>
                     <div className='grid gap-2 col-span-1'>
                        <Label htmlFor='days' className='text-sm font-semibold'>
                           Days
                        </Label>
                        <MultiSelect
                           options={DAYS || []}
                           values={form.days}
                           onValueChange={handleDaysChange}
                           placeholder='Select Days'
                           variant='inverted'
                           maxCount={0}
                        />
                     </div>
                  </div>
               )}
            </div>
            <DialogFooter>
               <DialogClose asChild>
                  <Button variant='outline' onClick={handleClose}>
                     Cancel
                  </Button>
               </DialogClose>
               <Button type='submit' onClick={() => void addUpdatePrompt()}>
                  Save
               </Button>
            </DialogFooter>
         </DialogContent>
      </Dialog>
   );
};

export default AddPromptDialog;
