@use '../../../sass/variable.scss';
.rdrStaticRangeLabel {
   padding: 4px;
   font-weight: 600;
}
.rdrDefinedRangesWrapper {
   width: 150px;
   padding: 5px;
}
.rdrMonthAndYearWrapper {
   height: 30px;
}
.rdrCalendarWrapper {
   font-size: 11px;
   .rdrDayNumber,
   .rdrWeekDay,
   .rdrMonthName {
      font-weight: 700;
   }
   .rdrMonthName {
      padding: 5px;
   }
}
.rdrMonthAndYearPickers {
   padding-top: 0;
   select {
      padding: 5px 20px 5px 5px;
   }
}
.rdrDateDisplay {
   margin: 5px;
}
.date-select {
   & .apply {
      background-color: #8714d2;
      color: white;
      border: 1px solid #8714d2;
      border-radius: 5px;
      position: revert-layer;
      width: fit-content;
      &:hover {
         background-color: white;
         color: #8714d2;
      }
   }
   & .btn {
      height: 32px;
      font-size: 13px;
      &:active {
         scale: 0.95;
      }
   }
   // Light Theme (Default)
   .rdrCalendarWrapper {
      background-color: #ffffff !important;
      color: #1a202c !important;
      font-size: 11px;

      .rdrDayNumber,
      .rdrWeekDay,
      .rdrMonthName {
         font-weight: 700;
         color: #1a202c;
      }
   }

   // Dark Theme
   [data-theme='dark'] .rdrCalendarWrapper {
      background-color: #1a202c !important;
      color: #ffffff !important;

      .rdrDayNumber,
      .rdrWeekDay,
      .rdrMonthName {
         color: #ffffff !important;
      }
   }

   .rdrMonthAndYearWrapper {
      background-color: #ffffff !important;
      height: 30px;
   }

   // Dark Theme
   [data-theme='dark'] .rdrMonthAndYearWrapper {
      background-color: #1a202c !important;
   }

   .rdrMonthAndYearPickers select {
      background-color: #ffffff !important;
      color: #1a202c !important;
   }

   // Dark Theme
   [data-theme='dark'] .rdrMonthAndYearPickers select {
      background-color: #2d3748 !important;
      color: #cbd5e0 !important;
   }

   .rdrStaticRange {
      background-color: #ffffff !important;
      color: #1a202c !important;

      [data-theme='dark'] & {
         background-color: #2d3748 !important;
         color: #ffffff !important;
      }

      &:hover {
         background-color: #edf2f7 !important;
         color: #7f56d9 !important;

         // [data-theme='dark'] & {
         //    background-color: $background_surface !important;
         // }
      }

      &.rdrStaticRangeSelected {
         background-color: #edf2f7 !important;
         color: #7f56d9 !important;
         // [data-theme='dark'] & {
         //    background-color: $background_surface !important;
         // }
      }
   }
   // .rdrStaticRange:hover .rdrStaticRangeLabel,
   // .rdrStaticRange:focus .rdrStaticRangeLabel {
   //    [data-theme='dark'] & {
   //       background-color: $background_surface !important;
   //       color: #ffffff !important;
   //    }
   // }
   .rdrDefinedRangesWrapper {
      background-color: #ffffff !important;
      border-color: #e2e8f0;

      [data-theme='dark'] & {
         color: #cbd5e0 !important;
         background-color: #2d3748 !important;
      }
   }

   .rdrDateDisplay {
      margin: 5px;

      input {
         background-color: #ffffff !important;
         color: #1a202c;

         [data-theme='dark'] & {
            background-color: #2d3748 !important;
            color: #cbd5e0 !important;
         }
      }
   }

   .rdrDayNumber span {
      color: #1a202c;

      [data-theme='dark'] & {
         color: #ffffff !important;
      }
   }

   .rdrDayPassive .rdrDayNumber span {
      color: #718096 !important;

      [data-theme='dark'] & {
         color: #a0aec0 !important;
      }
   }

   // Dark Theme
}
.date-label {
   span {
      padding-top: 3px;
   }
}
.kpi-date-filter {
   & .btn {
      height: 32px;
      font-size: 13px;
      &:active {
         scale: 0.95;
      }
   }

   select {
      background-color: #2d3748;
      color: #cbd5e0;
      border-color: #4a5568;

      &:hover {
         border-color: #63b3ed;
      }

      &:focus {
         border-color: #4299e1;
         box-shadow: 0 0 0 1px #4299e1;
      }

      option {
         background-color: #2d3748;
         color: #cbd5e0;

         &:hover {
            background-color: #4a5568;
         }

         &:disabled {
            color: #718096;
         }
      }
   }
}

// Range Select specific styles
.range-select-light {
   background-color: white !important;
   color: #1a202c !important;
   border-color: #e2e8f0 !important;

   option {
      background-color: white !important;
      color: #1a202c !important;
   }

   &:hover {
      border-color: #4299e1 !important;
   }
}

// Override default select styles
select {
   &.range-select-light {
      option {
         background-color: white !important;
         color: #1a202c !important;
      }
   }
}

.rdrSelected,
.rdrInRange,
.rdrStartEdge,
.rdrEndEdge {
   background: #7f56d9 !important;
   color: #ffffff !important;
}

// Dark Theme override
[data-theme='dark'] .rdrSelected,
[data-theme='dark'] .rdrInRange,
[data-theme='dark'] .rdrStartEdge,
[data-theme='dark'] .rdrEndEdge {
   background: #7f56d9 !important;
   color: #ffffff !important;
}

// Additional specificity for select options
.chakra-select__wrapper {
   select {
      &.range-select-light {
         option {
            background-color: white !important;
            color: #1a202c !important;
         }
      }
   }
}

// Force theme colors on dropdown
select:-internal-list-box {
   option {
      &.range-select-light {
         background-color: white !important;
         color: #1a202c !important;
      }
   }
}
/* === FIXED CONTINUOUS PURPLE RANGE + CLEAN HOVER === */

/* Continuous filled purple range */
.rdrInRange {
   background-color: #7f56d9 !important;
   color: #ffffff !important;
   border: none !important;
   border-radius: 0 !important;
   box-shadow: none !important;
}

/* Perfect rounded edges on start and end dates */
.rdrStartEdge,
.rdrEndEdge {
   background-color: #7f56d9 !important;
   color: #ffffff !important;
   box-shadow: none !important;
   border: none !important;
}

/* Round only outer sides — smooth connection with middle cells */
.rdrStartEdge {
   border-top-left-radius: 999px !important;
   border-bottom-left-radius: 999px !important;
   border-top-right-radius: 0 !important;
   border-bottom-right-radius: 0 !important;
}

.rdrEndEdge {
   border-top-right-radius: 999px !important;
   border-bottom-right-radius: 999px !important;
   border-top-left-radius: 0 !important;
   border-bottom-left-radius: 0 !important;
}

/* === Hover Preview (single clean purple outline, visible text) === */
.rdrDayHovered,
.rdrDayStartPreview,
.rdrDayInPreview,
.rdrDayEndPreview {
   background-color: transparent !important;
   border: none !important;
   box-shadow: none !important;
}

.rdrDayStartPreview,
.rdrDayEndPreview {
   box-shadow: 0 0 0 2px #7f56d9 inset !important;
   border-radius: 999px !important;
}

/* Keep hovered day text visible in purple */
.rdrDayHovered .rdrDayNumber span,
.rdrDayStartPreview .rdrDayNumber span,
.rdrDayEndPreview .rdrDayNumber span {
   color: #7f56d9 !important;
   font-weight: 600;
}

/* Soft inside preview fill (no box gaps) */
.rdrDayInPreview {
   background-color: rgba(127, 86, 217, 0.12) !important;
}

/* Fix seam gaps between range blocks */
.rdrInRange + .rdrInRange,
.rdrDayInPreview + .rdrDayInPreview {
   border-left: none !important;
}

/* Show transition for hover feedback */
.rdrDayHovered,
.rdrDayStartPreview,
.rdrDayEndPreview {
   transition: all 0.2s ease-in-out !important;
}

.rdrDateDisplayItemActive input,
.rdrDateDisplayItem input:focus {
   border-color: #7f56d9 !important;
   box-shadow: 0 0 0 1px #7f56d9 !important;
   outline: none !important;
}

// .date-select .rdrCalendarWrapper,
// .rdrDateDisplayWrapper,
// .date-select .rdrMonthAndYearWrapper {
//    [data-theme='dark'] & {
//       background-color: #2d3748 !important;
//       color: $text_color !important;
//    }
// }
// .rdrNextPrevButton {
//    [data-theme='dark'] & {
//       background-color: $background_light !important;
//       color: $text_color !important;
//    }
//    &:hover {
//       [data-theme='dark'] & {
//          background-color: $background_surface !important;
//       }
//    }
// }
// .rdrPprevButton i {
//    [data-theme='dark'] & {
//       border-color: transparent $text_color transparent transparent;
//    }
// }

// .rdrNextButton i {
//    [data-theme='dark'] & {
//       border-color: transparent transparent transparent $text_color;
//    }
// }
// .date-select .rdrMonthAndYearPickers select {
//    [data-theme='dark'] & {
//       background-color: #2d3748 !important;
//       color: $text_color !important;
//    }
// }

// .date-select .rdrCalendarWrapper .rdrDayNumber,
// .date-select .rdrCalendarWrapper .rdrWeekDay,
// .date-select .rdrCalendarWrapper .rdrMonthName {
//    [data-theme='dark'] & {
//       color: $text_color;
//    }
// }
// .rdrDayDisabled {
//    [data-theme='dark'] & {
//       background-color: #2d3748 !important;
//       color: $text_color !important;
//    }
// }
