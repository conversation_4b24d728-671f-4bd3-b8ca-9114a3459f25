import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON>aEye, FaEyeSlash } from 'react-icons/fa';
import {
   Box,
   Button,
   FormControl,
   Input,
   Image,
   useToast,
   FormErrorMessage,
   InputGroup,
   InputRightElement,
   Text,
   FormLabel,
   useColorModeValue,
} from '@chakra-ui/react';
import { useApiMutation } from '../../hooks/react-query-hooks';
import { loginStrings } from '../../utils/strings/login-strings';
import { appStrings } from '../../utils/strings/app-strings';
import { useAppDispatch } from '../../store/store';
import { LocalStorageService, Keys } from '../../utils/local-storage';

import ICON from '../../assets/image/flableaiBrandlogo.png';
import keys from '../../utils/strings/query-keys';
import authEndpoints from '../../api/service/auth';
import { setRegisterProgress } from '../../store/reducer/onboarding-reducer';
import { regex } from '../../utils/strings/auth-strings';

function Login() {
   const [showPassword, setShowPassword] = useState(false);
   const [form, setForm] = useState({
      email_address: '',
      password: '',
   });
   const [error, setError] = useState({
      email_address: '',
      password: '',
   });
   const navigate = useNavigate();
   const toast = useToast();
   const dispatch = useAppDispatch();

   const { isPending, mutate } = useApiMutation({
      queryKey: [keys.login],
      mutationFn: authEndpoints.login,
      onSuccessHandler: (response) => {
         if (response.message === 'OTP Verification Required.') {
            LocalStorageService.setItem(Keys.UserName, response.email_address);
            navigate('/auth/email-verification');
            toast({
               title: loginStrings.loginSuccessful,
               description: 'OTP verification pending.',
               status: 'success',
               duration: 5000,
               isClosable: true,
            });
         } else {
            if (response.register_progress !== 'Completed') {
               LocalStorageService.setItem(
                  Keys.UserName,
                  response.email_address,
               );
               dispatch(setRegisterProgress(response.register_progress));
               navigate('/onboarding');
            } else {
               const { email_address, refreshToken } = response;

               LocalStorageService.setItem(Keys.Token, refreshToken as string);
               LocalStorageService.setItem(Keys.UserName, email_address);

               navigate('/auth/choose-profile');
            }

            toast({
               title: loginStrings.loginSuccessful,
               description: response.message,
               status: 'success',
               duration: 5000,
               isClosable: true,
            });
         }
      },
      onError: (msg) => {
         toast({
            title: loginStrings.loginFailed,
            description: msg,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      },
   });

   const validateFields = (name: string, value: string) => {
      switch (name) {
         case 'email_address':
            if (!regex.email_address.test(value)) {
               setError((prevErrors) => ({
                  ...prevErrors,
                  email_address: 'Please enter a valid email address',
               }));
            } else {
               setError((prevErrors) => ({
                  ...prevErrors,
                  email_address: '',
               }));
            }
            break;

         case 'password':
            if (!regex.password.test(value)) {
               setError((prevErrors) => ({
                  ...prevErrors,
                  password:
                     'Password must be at least 8 characters long, must have atleast one number and one special character',
               }));
            } else {
               setError((prevErrors) => ({
                  ...prevErrors,
                  password: '',
               }));
            }
      }
   };

   const validateForm = () => {
      let isValid = true;
      let emailError = '';
      let passwordError = '';
      if (!form.email_address) {
         emailError = 'Email is required';
         isValid = false;
      } else if (!/\S+@\S+\.\S+/.test(form.email_address)) {
         emailError = 'Please enter a valid email address';
         isValid = false;
      }
      if (!form?.password) {
         passwordError = 'Password is required';
         isValid = false;
      } else if (form?.password?.length < 8) {
         passwordError =
            'Password must be at least 8 characters long, must have atleast one number and one special character';
         isValid = false;
      }
      setError({ email_address: emailError, password: passwordError });
      return isValid;
   };

   const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      setForm((prevForm) => ({
         ...prevForm,
         [name]: value,
      }));
      validateFields(name, value);
   };

   const handleNavigateToRegister = () => {
      navigate('/auth/register');
   };

   const handleSubmit = () => {
      if (!validateForm()) {
         return;
      }
      mutate(form);
   };

   return (
      <>
         <Box
            display='flex'
            justifyContent='center'
            alignItems='center'
            backgroundColor={useColorModeValue(
               'white',
               'var(--background-surface)',
            )}
            height='16%'
         >
            <Image src={ICON} alt='Flable Icon' h='35%' />
            <Box ml={2} fontSize='xl' fontWeight='bold'>
               {appStrings.companyName}
            </Box>
         </Box>
         <FormControl
            height='19%'
            id='email_address'
            isInvalid={!!error.email_address}
         >
            <FormLabel ml={1} fontSize={['xs', 'sm']}>
               Email Address *
            </FormLabel>
            <Input
               backgroundColor={useColorModeValue('white', 'var(--controls)')}
               type='email'
               name='email_address'
               value={form.email_address}
               onChange={handleChange}
               placeholder='Enter your email address'
               variant='outline'
               height='45%'
            />
            {error.email_address && (
               <FormErrorMessage fontSize='xs'>
                  {error.email_address}
               </FormErrorMessage>
            )}
         </FormControl>
         <FormControl height='19%' id='password' isInvalid={!!error.password}>
            <FormLabel ml={1} fontSize={['xs', 'sm']}>
               Password *
            </FormLabel>
            <InputGroup height='45%'>
               <Input
                  backgroundColor={useColorModeValue(
                     'white',
                     'var(--controls)',
                  )}
                  type={showPassword ? 'text' : 'password'}
                  name='password'
                  value={form.password}
                  onChange={handleChange}
                  placeholder='Enter your password'
                  variant='outline'
                  height='100%'
               />
               <InputRightElement>
                  <Button
                     h='1.75rem'
                     size='sm'
                     onClick={() => setShowPassword(!showPassword)}
                     variant='ghost'
                  >
                     {showPassword ? <FaEyeSlash /> : <FaEye />}
                  </Button>
               </InputRightElement>
            </InputGroup>
            {error.password && (
               <FormErrorMessage fontSize='xs'>
                  {error.password}
               </FormErrorMessage>
            )}
         </FormControl>
        <Button
   width="full"
   disabled={isPending || !form.email_address || !form.password}
   mt={5}
   height="6%"
   bgGradient="linear(to-r, #FB479F, #7F56D9)"
   _hover={{
      bgGradient: "linear(to-r, #FF66B2, #9C7CFF)",
   }}
   color="white"
   onClick={handleSubmit}
   size="sm"
   isLoading={isPending}
   _disabled={{
      opacity: 0.6,
      cursor: "not-allowed",
      bgGradient: "linear(to-r, #FB479F, #7F56D9)",
   }}
>
   {loginStrings.login}
</Button>

         <Text
            as='span'
            mt={5}
            float='right'
            fontSize='xs'
            height='5%'
            sx={{
               cursor: 'pointer',
               textDecoration: 'underline dashed',
               '&:hover': {
                  textDecoration: 'underline',
                  fontWeight: 'semibold',
               },
            }}
            onClick={() => navigate('/auth/forgot-password')}
         >
            Forgot password?
         </Text>
         <Text mt={10} textAlign='center' fontSize='sm' height='5%'>
            Don't have an account?{' '}
            <Text
               as='span'
               onClick={() => handleNavigateToRegister()}
               sx={{
                  display: 'inline',
                  color: '#7F56D9',
                  cursor: 'pointer',
                  '&:hover': { textDecoration: 'underline' },
               }}
            >
               Register
            </Text>
         </Text>
      </>
   );
}

export default Login;
