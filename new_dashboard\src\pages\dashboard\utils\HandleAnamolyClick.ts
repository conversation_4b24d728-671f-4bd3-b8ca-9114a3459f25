import { AppDispatch } from '@/store/store';
import {
   setCurrentSessionID,
   setCurrentMode,
   setKpiPrompts,
   setKpiMetadata,
   clearKpiMetadata,
} from '@/store/reducer/analytics-agent-reducer';
import { closeModal } from '@/store/reducer/modal-reducer';
import {
   META_KPI_PROMPTS,
   GOOGLE_ADS_KPI_PROMPTS,
   MINIMUM_DATE_DIFFERENCE_DAYS,
} from '@/pages/marco/utils/analytics-agent/constants';
import { LocalStorageService, Keys } from '@/utils/local-storage';
import { AuthUser } from '@/types/auth';
import { getCurrencyThreshold } from '@/pages/pulse/utils/helper';
import { NavigateFunction } from 'react-router-dom';
import { KpiDataWithMeta } from './interface';

export const handleRootCauseAction = async ({
   kpiDetails,
   prevRange,
   kpiCurrency,
   dispatch,
   navigate,
}: {
   kpiDetails: KpiDataWithMeta;
   prevRange: { start: string; end: string };
   kpiCurrency: { meta: string; google: string };
   dispatch: AppDispatch;
   navigate: NavigateFunction;
}) => {
   if (kpiDetails?.current_allData?.length > 0) {
      const dates = kpiDetails.current_allData.map((x) => String(x.date));
      const startDate = (dates[0] || '').split('T')[0];
      const endDate = (dates[dates.length - 1] || '').split('T')[0];

      const userDetails = LocalStorageService.getItem<AuthUser>(
         Keys.FlableUserDetails,
      );
      const client_id = userDetails?.client_id ?? '';

      // Resolve KPI key directly from domain-aligned names
      const isGoogleAds = kpiDetails.category === 'googleads';
      const promptSet = isGoogleAds ? GOOGLE_ADS_KPI_PROMPTS : META_KPI_PROMPTS;

      const rawName = String(kpiDetails.kpi_names || '').trim();
      const normalized = rawName
         .toLowerCase()
         .replace(/\s+/g, '_')
         .replace(/%/g, '');
      // Handle simple plural → singular for common cases (e.g., video_views → video_view)
      const singular = normalized.endsWith('s')
         ? normalized.slice(0, -1)
         : normalized;
      const tryCandidates = [rawName, normalized, singular];

      let kpi = '';
      for (const cand of tryCandidates) {
         if (cand in promptSet) {
            kpi = cand as keyof typeof promptSet as string;
            break;
         }
      }
      if (!kpi) {
         console.error('No matching KPI key found for prompt set from', {
            kpi_names: kpiDetails.kpi_names,
            kpi_display_name: kpiDetails.kpi_display_name,
            tried: tryCandidates,
            category: kpiDetails.category,
         });
         return;
      }

      const platformCurrency = isGoogleAds
         ? kpiCurrency?.google
         : kpiCurrency?.meta;

      const currency =
         String(platformCurrency || '')
            .trim()
            .toUpperCase() || 'INR';
      const currencyThreshold = await getCurrencyThreshold(currency);

      // Normalize all dates to YYYY-MM-DD format
      const normalizedStartDate = String(startDate).split(/[T\s]/)[0];
      const normalizedEndDate = String(endDate).split(/[T\s]/)[0];
      const normalizedPrevStart = String(prevRange.start).split(/[T\s]/)[0];
      const normalizedPrevEnd = String(prevRange.end).split(/[T\s]/)[0];

      const currentValue = Number(kpiDetails.current_total_value || 0);
      const previousValue = Number(kpiDetails.previous_total_value || 0);

      let percentageChange = 0;
      let changeDirection = 'no change';

      if (previousValue !== 0) {
         percentageChange =
            ((currentValue - previousValue) / previousValue) * 100;
         changeDirection = percentageChange > 0 ? 'increase' : 'decrease';
      } else if (currentValue !== 0) {
         percentageChange = 100;
         changeDirection = 'increase';
      } else {
         percentageChange = 0;
         changeDirection = 'no change';
      }

      const absPercentageChange = Math.abs(percentageChange).toFixed(1);
      const platformName =
         kpiDetails.category === 'googleads' ? 'Google Ads' : 'Meta Ads';

      const displayStart = String(startDate).split(/[T\s]/)[0];
      const displayEnd = String(endDate).split(/[T\s]/)[0];
      const displayPrevStart = String(prevRange.start).split(/[T\s]/)[0];
      const displayPrevEnd = String(prevRange.end).split(/[T\s]/)[0];
      const displayPrompt = `Diagnostic analysis: Root cause of why ${kpi} ${changeDirection} by ${absPercentageChange}% in ${platformName} from ${displayStart} to ${displayEnd} compared to ${displayPrevStart} to ${displayPrevEnd}?`;

      const buildAiPrompt = (
         client_id: string,
         kpi: string,
         start_date: string,
         end_date: string,
         currency: string,
      ): string => {
         // Use stored YYYY-MM-DD from local strings to avoid UTC shift
         const prev_start_str = String(prevRange.start).split('T')[0];
         const prev_end_str = String(prevRange.end).split('T')[0];

         const start = new Date(start_date);
         const end = new Date(end_date);
         const dateDiffInDays = Math.ceil(
            (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24),
         );
         const isDateDiffGe6 = dateDiffInDays >= MINIMUM_DATE_DIFFERENCE_DAYS;

         const isGoogleAds = kpiDetails.category === 'googleads';
         let kpiPrompt: string | undefined;
         if (isGoogleAds) {
            kpiPrompt =
               GOOGLE_ADS_KPI_PROMPTS[
                  kpi as keyof typeof GOOGLE_ADS_KPI_PROMPTS
               ];
         } else {
            kpiPrompt = META_KPI_PROMPTS[kpi as keyof typeof META_KPI_PROMPTS];
         }

         if (!kpiPrompt) {
            console.error(
               'No prompt template found for KPI:',
               kpi,
               'in category:',
               kpiDetails.category,
            );
            return '';
         }

         let processedPrompt = kpiPrompt
            .replace(/\{\{client_id\}\}/g, client_id)
            .replace(/\{\{KPI\}\}/g, kpi)
            .replace(/\{\{start_date\}\}/g, start_date)
            .replace(/\{\{end_date\}\}/g, end_date)
            .replace(/\{\{prev_start_str\}\}/g, prev_start_str)
            .replace(/\{\{prev_end_str\}\}/g, prev_end_str)
            .replace(/\{\{currency\}\}/g, currency)
            .replace(/\{\{currency_threshold\}\}/g, currencyThreshold);

         if (!isDateDiffGe6) {
            processedPrompt = processedPrompt.replace(
               /\{\{#if date_diff_ge_6\}\}([\s\S]*?)\{\{\/if\}\}/g,
               '',
            );
         } else {
            processedPrompt = processedPrompt.replace(
               /\{\{#if date_diff_ge_6\}\}([\s\S]*?)\{\{\/if\}\}/g,
               '$1',
            );
         }

         return processedPrompt;
      };

      const aiPrompt = buildAiPrompt(
         client_id,
         kpi,
         normalizedStartDate,
         normalizedEndDate,
         currency,
      );

      if (aiPrompt) {
         dispatch(setCurrentSessionID(''));
         dispatch(setCurrentMode('data-analyst'));

         dispatch(closeModal());

         const kpiMetadata = {
            kpi: String(kpi),
            currency: String(currency),
            currency_threshold: currencyThreshold,
            start_date: normalizedStartDate,
            end_date: normalizedEndDate,
            prev_start_date: normalizedPrevStart,
            prev_end_date: normalizedPrevEnd,
            percentage_change: String(percentageChange),
            source: 'root-cause' as const,
            platform: isGoogleAds
               ? ('GOOGLE_ADS_KPI_PROMPTS' as const)
               : ('META_KPI_PROMPTS' as const),
         };

         // Clear previous KPI metadata and store new metadata in Redux
         dispatch(clearKpiMetadata());
         dispatch(setKpiMetadata(kpiMetadata));

         dispatch(
            setKpiPrompts({
               displayPrompt,
               aiPrompt,
            }),
         );

         navigate('/marco/analytics-agent');
      } else {
         console.error('Failed to generate AI prompt for KPI:', kpi);
      }
   }
};
