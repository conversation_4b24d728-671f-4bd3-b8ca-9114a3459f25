import {
   SidebarGroup,
   SidebarMenu,
   SidebarMenuAction,
   SidebarMenuButton,
   SidebarMenuItem,
   SidebarMenuSub,
   SidebarMenuSubItem,
   SidebarMenuSubButton,
   useSidebar,
} from '@/components/ui/sidebar';
import {
   Collapsible,
   CollapsibleContent,
   CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { AuthUser } from '@/types/auth';
import { useMemo } from 'react';
import { ChevronRight } from 'lucide-react';
import dashboardIcon from '@/assets/icons/dashboard-sidebar-icon.svg';
import marcoIcon from '@/assets/icons/marco-sidebar-icon.svg';
import pulseIcon from '@/assets/icons/pulse-sidebar-icon.svg';
import integrationsIcon from '@/assets/icons/integrations-sidebar-icon.svg';
import performanceInsightIcon from '@/assets/icons/performance-insights-icon.svg';
import webInsightIcon from '@/assets/icons/web-insights-icon.svg';
import alertsIcon from '@/assets/icons/Bell Bing.svg';
import bookmarkIcon from '@/assets/icons/bookmark-sidebar-icon.svg';
import { useLocation, useNavigate } from 'react-router-dom';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import { cn } from '@/utils';

const APP_SIDEBAR_CONTENT = [
   {
      title: 'Chats',
      url: '/marco',
      icon: marcoIcon,
      isActive: true,
      matchPrefix: [
         '/marco',
         '/marco/analytics-agent',
         '/marco/meta-ads-manager-agent',
         '/marco/meta-ads-manager-auto',
      ],
   },
   {
      title: 'Dashboard',
      url: '/dashboard',
      icon: dashboardIcon,
      matchPrefix: ['/dashboard'],
      items: [],
   },
   {
      title: 'Pulse',
      url: '/pulse/performance-insights',
      icon: pulseIcon,
      matchPrefix: ['/pulse'],
      items: [
         {
            title: 'Performance Insights',
            url: '/pulse/performance-insights',
            icon: performanceInsightIcon,
            matchPrefix: ['/pulse/performance-insights'],
         },
         {
            title: 'Web Insights',
            url: '/pulse/web-insights',
            icon: webInsightIcon,
            matchPrefix: ['/pulse/web-insights'],
         },
      ],
   },
   {
      title: 'Bookmarks',
      url: '/bookmarks',
      icon: bookmarkIcon,
      matchPrefix: ['/bookmarks'],
   },
   {
      title: 'Alerts',
      url: '/alerts',
      icon: alertsIcon,
      matchPrefix: ['/alerts'],
   },
];

const AppSidebarContent = () => {
   const navigate = useNavigate();
   const location = useLocation();

   const { open } = useSidebar();

   const currentPath = location.pathname;

   const userDetails = LocalStorageService.getItem(
      Keys.FlableUserDetails,
   ) as AuthUser;

   const handleClick = (url: string) => {
      navigate(url);
   };

   const isMatch = (prefixes: string[]) => {
      if (!prefixes) return false;
      return prefixes.some((prefix) => currentPath.startsWith(prefix));
   };

   const sidebarContent = useMemo(() => {
      const baseContent = [...APP_SIDEBAR_CONTENT];

      if (userDetails?.user_role === 'Admin') {
         baseContent.push({
            title: 'Integrations',
            url: '/integrations',
            icon: integrationsIcon,
            items: [],
            matchPrefix: ['/integrations'],
         });
      }

      return baseContent;
   }, [userDetails?.user_role]);

   return (
      <SidebarGroup className='mt-4'>
         <SidebarMenu className='flex flex-col gap-3'>
            {sidebarContent.map((item) => (
               <Collapsible
                  key={item.title}
                  asChild
                  defaultOpen={item.isActive}
               >
                  <SidebarMenuItem>
                     <SidebarMenuButton
                        className='mb-2 mx-auto py-5 hover:cursor-pointer text-black data-[active=true]:text-[##1A1A1D] data-[active=true]:bg-[#E9E1FF]'
                        asChild
                        tooltip={{
                           children: item.title,
                           className:
                              'z-100 bg-[#3c76e1] rounded-sm px-2 py-1 ml-1 text-white [&_svg]:fill-[#3c76e1] text-sm font-bold',
                        }}
                        isActive={isMatch(item.matchPrefix)}
                        onClick={() => handleClick(item.url)}
                     >
                        <div>
                           <img
                              src={item.icon}
                              alt={`${item.title} icon`}
                              className={cn(open ? 'w-[24px]' : 'w-[26px]')}
                              style={{
                                 filter: isMatch(item.matchPrefix)
                                    ? 'brightness(0) saturate(100%) invert(43%) sepia(25%) saturate(1364%) hue-rotate(227deg) brightness(94%) contrast(92%)'
                                    : 'none',
                              }}
                           />
                           <span className='text-[16px] font-normal font-poppins '>
                              {item.title}
                           </span>
                        </div>
                     </SidebarMenuButton>
                     {item?.items?.length ? (
                        <>
                           <CollapsibleTrigger
                              className='hover:cursor-pointer'
                              asChild
                           >
                              <SidebarMenuAction className='data-[state=open]:rotate-90 text-blue mt-1'>
                                 <ChevronRight
                                    style={{
                                       filter: isMatch(item.matchPrefix)
                                          ? 'brightness(0) saturate(100%) invert(43%) sepia(25%) saturate(1364%) hue-rotate(227deg) brightness(94%) contrast(92%)'
                                          : 'none',
                                    }}
                                 />
                                 <span className='sr-only'>Toggle</span>
                              </SidebarMenuAction>
                           </CollapsibleTrigger>
                           <CollapsibleContent>
                              <SidebarMenuSub>
                                 {item.items?.map((subItem) => (
                                    <SidebarMenuSubItem
                                       key={subItem.title}
                                       className='mt-3 mb-3'
                                    >
                                       <SidebarMenuSubButton
                                          className='hover:cursor-pointer text-black data-[active=true]:text-[#7F56D9] data-[active=true]:bg-white'
                                          isActive={isMatch(
                                             subItem.matchPrefix,
                                          )}
                                          asChild
                                          onClick={() =>
                                             handleClick(subItem.url)
                                          }
                                       >
                                          <div>
                                             <img
                                                src={subItem?.icon}
                                                alt={`${subItem.title} icon`}
                                                className='w-[20px]'
                                                style={{
                                                   filter: isMatch(
                                                      subItem.matchPrefix,
                                                   )
                                                      ? 'brightness(0) saturate(100%) invert(43%) sepia(25%) saturate(1364%) hue-rotate(227deg) brightness(94%) contrast(92%)'
                                                      : 'none',
                                                }}
                                             />
                                             <span className='text-[16px] font-normal font-poppins'>
                                                {subItem.title}
                                             </span>
                                          </div>
                                       </SidebarMenuSubButton>
                                    </SidebarMenuSubItem>
                                 ))}
                              </SidebarMenuSub>
                           </CollapsibleContent>
                        </>
                     ) : null}
                  </SidebarMenuItem>
               </Collapsible>
            ))}
         </SidebarMenu>
      </SidebarGroup>
   );
};

export default AppSidebarContent;
