import {
   Sidebar,
   SidebarContent,
   SidebarFooter,
   SidebarHeader,
   SidebarTrigger,
   useSidebar,
} from '@/components/ui/sidebar';
//import FlableLogoWithName from '@/assets/image/flableicon.png';
//import flablenewLogoWithName from '@/assets/image/BrandLogo.png';
//import FlableLogo from '@/assets/icons/icon.png';
import flablelogo from '@/assets/image/flableaiBrandlogo.png';
import AppSidebarContent from './app-sidebar-content';
import AppSidebarFooter from './app-sidebar-footer';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import { AuthUser } from '@/types/auth';
import { cn } from '@/utils';

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
   const { open } = useSidebar();

   const userDetails = LocalStorageService.getItem(
      Keys.FlableUserDetails,
   ) as AuthUser;

   return (
      <Sidebar
         className='h-[100vh] max-h-[100vh] overflow-hidden'
         collapsible='icon'
         {...props}
      >
         <SidebarHeader className='!bg-[#ffffff]'>
            <div className='flex items-center justify-between'>
               <div
                  className={cn(
                     'flex items-center gap-2 mt-2',
                     open ? 'ml-6' : 'ml-4',
                  )}
               >
                  <img
                     src={flablelogo}
                     alt='Flable logo'
                     className={cn(open ? 'w-[28px]' : 'w-[26px]')}
                  />
                  {open && (
                     <span className='font-poppins text-[25px]  text-[#1A1A1D] tracking-tight'>
                        flable.ai
                     </span>
                  )}
               </div>
               {open && <SidebarTrigger className='hover:cursor-pointer' />}
            </div>
         </SidebarHeader>
         <SidebarContent
            className={cn(open && 'px-4', '!bg-[#ffffff] flex flex-col h-full')}
         >
            <AppSidebarContent />
         </SidebarContent>
         <SidebarFooter className={cn(open ? 'px-4' : 'p-0')}>
            {userDetails?.user_role === 'Admin' && <AppSidebarFooter />}
         </SidebarFooter>
      </Sidebar>
   );
}
